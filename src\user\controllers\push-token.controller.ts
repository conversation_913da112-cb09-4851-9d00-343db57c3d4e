/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Post, Delete, Body, Param, Put, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { ChatuserService } from '../services/v2/chatuser.service';
import { GetUser } from 'src/get-user.decorator';
import { ChatUser } from 'src/models/chat-user.model';
import { IsString, IsNotEmpty } from 'class-validator';

class UpdatePushTokenDto {
    @IsString()
    @IsNotEmpty()
    pushToken: string;
}

@ApiTags('Push Tokens')
@Controller('push-tokens')
export class PushTokenController {
    constructor(private readonly chatUserService: ChatuserService) {}

    @Post()
    @ApiOperation({ summary: 'Update user push token' })
    @ApiBody({ type: UpdatePushTokenDto })
    @ApiResponse({ status: 200, description: 'Push token updated successfully' })
    @ApiResponse({ status: 400, description: 'Bad request - invalid push token' })
    async updatePushToken(
        @GetUser() user: ChatUser,
        @Body() updatePushTokenDto: UpdatePushTokenDto
    ) {
        return await this.chatUserService.updatePushToken(user.id, updatePushTokenDto.pushToken);
    }

    @Put('by-jobpro-id/:jobProUserId')
    @ApiOperation({ summary: 'Update user push token by JobPro User ID' })
    @ApiParam({ name: 'jobProUserId', description: 'JobPro User ID' })
    @ApiBody({ type: UpdatePushTokenDto })
    @ApiResponse({ status: 200, description: 'Push token updated successfully' })
    @ApiResponse({ status: 404, description: 'User not found' })
    async updatePushTokenByJobProId(
        @Param('jobProUserId') jobProUserId: string,
        @Body() updatePushTokenDto: UpdatePushTokenDto
    ) {
        return await this.chatUserService.updatePushTokenByJobProId(jobProUserId, updatePushTokenDto.pushToken);
    }

    @Delete()
    @ApiOperation({ summary: 'Remove user push token' })
    @ApiResponse({ status: 200, description: 'Push token removed successfully' })
    async removePushToken(@GetUser() user: ChatUser) {
        return await this.chatUserService.removePushToken(user.id);
    }

    @Delete('by-jobpro-id/:jobProUserId')
    @ApiOperation({ summary: 'Remove user push token by JobPro User ID' })
    @ApiParam({ name: 'jobProUserId', description: 'JobPro User ID' })
    @ApiResponse({ status: 200, description: 'Push token removed successfully' })
    @ApiResponse({ status: 404, description: 'User not found' })
    async removePushTokenByJobProId(@Param('jobProUserId') jobProUserId: string) {
        return await this.chatUserService.removePushTokenByJobProId(jobProUserId);
    }

    @Get('token')
    @ApiOperation({ summary: 'Get user push token' })
    @ApiResponse({ status: 200, description: 'Push token retrieved successfully' })
    async getPushToken(@GetUser() user: ChatUser) {
        const pushToken = await this.chatUserService.getPushToken(user.id);
        return { pushToken };
    }

    @Get('has-token')
    @ApiOperation({ summary: 'Check if user has push token' })
    @ApiResponse({ status: 200, description: 'Push token status retrieved successfully' })
    async hasPushToken(@GetUser() user: ChatUser) {
        const hasToken = await this.chatUserService.hasPushToken(user.id);
        return { hasToken };
    }
}
