/*
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, Types } from 'mongoose';
import { ChatService } from 'src/chat/services/chat.service';
import { ChatUserDto, CreateChatUserDto, UpdateChatUserDto } from 'src/dto/dtos';
import { ChatUser } from 'src/models/chat-user.model';
import { Chat } from 'src/models/chat.model';
import crypto from 'node:crypto';
import { USER_STATUSES } from 'src/messages/dto/event.payload.dto';
import { Socket } from 'socket.io';
import { LogService } from 'src/log/log.service';

@Injectable()
export class ChatuserService {

    constructor(
        @InjectModel(ChatUser.name) private readonly chatUserModel: Model<ChatUser>,
        @InjectModel(Chat.name) private readonly chatModel: Model<Chat>,
        private logService: LogService
    ) {
        // this.removeKeys()
    }
    async removeKeys() {
        await this.chatUserModel.updateMany({ privateKey: { $exists: true } },
            { $unset: { privateKey: "", publicKey: '' } })
    }
    async removeSocket(id: string, socket: string) {
        await this.chatUserModel.updateOne(
            { _id: id }, // Match by user ID
            { $pull: { sockets: socket } } // Remove socket from the sockets array
        );
    }

    async addSocket(userId: string, socket: string) {
        this.logService.log('Adding socket');
        await this.chatUserModel.updateOne(
            { _id: userId, sockets: { $ne: socket } }, // Ensure socket is not already in the array
            { $push: { sockets: socket } } // Add the socket to the array
        );
    }

    async updateStatus(status: string, userId: string,) {
        if (!status) throw new BadRequestException('status is required');
        await this.chatUserModel.updateOne({ _id: userId }, { status });
        return await this.findUser(userId)
    }
    async switchActiveChat(chatId: string, user: ChatUserDto) {
        if (!chatId) throw new BadRequestException('Chat id is required!');
        const update = await this.chatUserModel.updateOne({ _id: user.id }, { activeChatId: chatId })
        if (update.modifiedCount) {
            return { message: 'Active chat switched', status: 'Success' }
        }
        return { message: 'Active chat switching failed', status: 'Failed' }
    }
    async deleteUser(id: any) {
        await this.chatUserModel.deleteOne({ _id: id });
    }

    async deleteManyUsers(idx: string[]) {
        await this.chatUserModel.deleteMany({ _id: { $in: idx } })
    }
    async findAUserWithTenantId(tenantId: string) {
        return await this.chatUserModel
            .findOne({ tenantId })
            .sort({ registrationDate: 1 })

    }
    async findUserByEmail(email: string) {
        return await this.chatUserModel.findOne({ email })
    }

    async createKeys(userId: string) {
        const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem',
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem',
            },
        });
        this.logService.log('keys: ', privateKey, publicKey)
        this.logService.log('user id: ', userId)
        const up = await this.updateKeys(userId, privateKey, publicKey);
        return { privateKey, publicKey };
    }
    async updateKeys(userId: string, privateKey: string, publicKey: string) {
        return await this.chatUserModel.updateOne({ _id: userId }, { privateKey, publicKey })
    }

    async portUser() {
        try {
            // Connect to the new MongoDB database
            const con = await mongoose.connect('mongodb+srv://kingstanley:<EMAIL>/chatserviceDB?retryWrites=true&w=majority', {
                // useNewUrlParser: true,
                // useUnifiedTopology: true,
            });
            const savedList = [];
            // Once the connection is established, proceed with porting logic
            const usersCursor = mongoose.connection.collection('chatusers').find({});
            const usersArray = await usersCursor.toArray();

            for (const user of usersArray) {
                // savedList.push(user);
                const found = await this.chatUserModel.findOne({ jobProUserId: user.jobProUserId });

                if (!found) {
                    const saved = await this.chatUserModel.create(user);
                    savedList.push(saved); this.logService.log(`User ${user.jobProUserId} has been ported.`);
                }
            }
            return savedList;
        } catch (error) {
            this.logService.error('Error porting users:', error);
        } finally {
            // Close the connection
            await mongoose.connection.close();
        }
    }

    async search(name: string, date: string) {
        const query = {}
        if (name) {
            if (name) {
                query['email'] = { $regex: name, $options: 'i' };
            }
        }
        const users = await this.chatUserModel.find(query,);
        return users;

    }
    async findUsersByTenantId(tenantId: string) {
        return await this.chatUserModel.find({ tenantId },);
    }
    async findUserObjectsByJobProId(jobProUserId: string) {
        return await this.chatUserModel.find({ jobProUserId });
    } async findUserByJobProId(jobProUserId: string): Promise<ChatUser> {
        return await this.chatUserModel.findOne({ jobProUserId }, { privateKey: 0, publicKey: 0 })
    }
    async addUserToGeneralChat(tenantId: string, userId: string, jobProUserId: string) {
        const generalChat = await this.chatModel.findOne({ tenantId, isGeneral: true },);
        if (generalChat) {
            await this.chatModel.updateOne({ _id: generalChat.id, users: { $ne: userId }, jobProUserIds: { $ne: jobProUserId } }, { $push: { users: userId, jobProUserIds: jobProUserId } })
        }
    }
    async createUser(data: CreateChatUserDto): Promise<ChatUser> {
        const user = (await this.chatUserModel.findOne({ jobProUserId: data.jobProUserId }))?.toJSON();
        const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem',
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem',
            },
        });

        if (user && user.tenantId.length > 0 && data.tenantId != 'undefined') {
            await this.addTenantIdToUser(data);
            this.addUserToGeneralChat(data.tenantId, user.id, user.jobProUserId);
            // Return the updated user data
            return await this.findUserByJobProId(data.jobProUserId);
        } else {
            if (data.jobProUserId && data.jobProUserId != 'undefined' && data.tenantId && data.tenantId != 'undefined') {
                const toSave = await this.chatUserModel.create({ ...data, tenantId: [data.tenantId], publicKey, privateKey });
                this.addUserToGeneralChat(data.tenantId, toSave.id, toSave.jobProUserId);
                return (await toSave.save()).toJSON();
            }
        }
    }
    async addTenantIdToUser(data: CreateChatUserDto) {
        if (data.tenantId && data.jobProUserId && data.tenantId != 'undefined' && data.jobProUserId != 'undefined')
            await this.chatUserModel.updateOne({ jobProUserId: data.jobProUserId, tenantId: { $ne: data.tenantId } },
                { $push: { tenantId: data.tenantId } });
    }

    async updateUserColorPreference(data: UpdateChatUserDto) {
        await this.findUser(data.id.trim());
        return await this.chatUserModel.findOneAndUpdate({ _id: data.id }, { colorPreference: data.colorPreference });
    }

    async addChatToUser(chatId: string, userId: string) {
        await this.findUser(userId);
        this.chatUserModel.updateOne({ _id: new Types.ObjectId(userId) }, { $push: { chats: new Types.ObjectId(chatId) } })
    }
    async findUsersByJobProIds(jobProIds: string[], tenantId: string, emails: string[]) {
        this.logService.log('Jobpro user ids: ', jobProIds)
        const list = await this.chatUserModel.find({ jobProUserId: { $in: jobProIds } },) as ChatUser[];
        // this.logService.log('user list: ', list)
        for (let i = 0; i < jobProIds.length; i++) {
            const exist = list.find(user => user.jobProUserId == jobProIds[i]);
            if (!exist) {
                const user = await this.createUser({ jobProUserId: jobProIds[i], tenantId, email: emails[i] });
                list.push(user);
            }
        }
        // this.logService.log('updated user id list: ', list);
        return list;
    }
    async findUserWithChats(id: string) {
        const user = (await this.chatUserModel.findOne({ _id: id }, { privateKey: 0, publicKey: 0 })).populated('chats');
        // user.chats.
        return user;
    }

    async findUser(id: string): Promise<ChatUser> {
        this.logService.log('looking for user with id: ', id)
        const found = await this.chatUserModel.findOne({ _id: id }, { privateKey: 0, publicKey: 0 });

        if (!found) { //throw new NotFoundException('User not found!') 
            return null;
        }
        return found.toJSON();
    }
    async removeChatFromUser(userId: string, chatId: string) {
        await this.chatUserModel.updateOne({ _id: userId }, { $pull: { chats: chatId } })
    }

    /**
     * Update user's push token for mobile notifications
     */
    async updatePushToken(userId: string, pushToken: string): Promise<ChatUser> {
        if (!pushToken) throw new BadRequestException('Push token is required');

        await this.chatUserModel.updateOne({ _id: userId }, { pushToken });
        return await this.findUser(userId);
    }

    /**
     * Update user's push token by jobProUserId
     */
    async updatePushTokenByJobProId(jobProUserId: string, pushToken: string): Promise<ChatUser> {
        if (!pushToken) throw new BadRequestException('Push token is required');

        const user = await this.findUserByJobProId(jobProUserId);
        if (!user) throw new NotFoundException('User not found');

        await this.chatUserModel.updateOne({ jobProUserId }, { pushToken });
        return await this.findUser(user.id);
    }

    /**
     * Remove user's push token (e.g., when user logs out)
     */
    async removePushToken(userId: string): Promise<ChatUser> {
        await this.chatUserModel.updateOne({ _id: userId }, { $unset: { pushToken: "" } });
        return await this.findUser(userId);
    }

    /**
     * Remove user's push token by jobProUserId
     */
    async removePushTokenByJobProId(jobProUserId: string): Promise<ChatUser> {
        const user = await this.findUserByJobProId(jobProUserId);
        if (!user) throw new NotFoundException('User not found');

        await this.chatUserModel.updateOne({ jobProUserId }, { $unset: { pushToken: "" } });
        return await this.findUser(user.id);
    }

    /**
     * Get user's push token
     */
    async getPushToken(userId: string): Promise<string | null> {
        const user = await this.chatUserModel.findOne({ _id: userId }, { pushToken: 1 });
        return user?.pushToken || null;
    }

    /**
     * Check if user has a valid push token
     */
    async hasPushToken(userId: string): Promise<boolean> {
        const user = await this.chatUserModel.findOne({ _id: userId }, { pushToken: 1 });
        return !!(user?.pushToken);
    }
}

