/*
https://docs.nestjs.com/websockets/gateways#gateways
*/

import { MessageBody, SubscribeMessage, WebSocketGateway, WebSocketServer, OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit, ConnectedSocket, WsException } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ChatService } from './chat.service';
import { ChatMessageService } from './chat-message.service';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { Payload } from 'src/call/events.enum';
import { CHAT_EVENT, CHAT_EVENTS, MESSAGE_EVENT, USER_STATUSES } from 'src/messages/dto/event.payload.dto';
import { ACTIVITY_TYPE, ChatActivityDto, ChatDto, ChatMessageDto, ChatUserDto, INVITE_TYPE, UpdateChatMessageDto, UpdateChatUserDto } from 'src/dto/dtos';
import { LogService } from 'src/log/log.service';
import { UpdateMessageDto } from 'src/messages/dto/update-message.dto';
import { Types } from 'mongoose';
import { Call } from 'src/messages/models/chat_call.model';
import { CHAT_CALL_STATUS, ChatCallDto, CHAT_CALL_FORMAT } from 'src/messages/dto/chat-call.dto';
import { MESSAGE_TYPE } from 'src/messages/dto/message_type.enum';
import { v4 } from 'uuid'
import { BadRequestException, UseGuards } from '@nestjs/common';
import { WSAuthGuard } from 'src/user/ws.auth.guard';
import { ChatCallService } from 'src/messages/service/chat-call.service';
import { ChatUser } from 'src/models/chat-user.model';
import { MessageScheduleService } from './message-schedule.service';
import { Cron, CronExpression } from '@nestjs/schedule';
// import { MessageType } from '@socket.io/redis-adapter/dist/cluster-adapter';
import { ChatInviteService } from './chat-invite.service';
import { ActivityService } from './activity.service';
import { RedisService } from '../redis.service';
import { RabbitMQService } from 'src/rabbitmq/rabbitmq.service';
import { Applications, NotificationMessageModel, NotificationType, RabbitMQQueues } from 'src/rabbitmq/dtos';
import { UserModule } from 'src/user/user.module';
import { Chat, CHAT_TYPE } from 'src/models/chat.model';
import { JwtService } from '@nestjs/jwt';
import { MongoClient } from 'mongodb';
import { ConfigService } from '@nestjs/config';
import { Ctx, EventPattern, MessagePattern, Payload as Payloads, RmqContext } from '@nestjs/microservices';
import { pubClient, subClient } from 'src/chat/redis.adapter';
import { createAdapter } from '@socket.io/redis-streams-adapter';
import { MailerService } from '@nestjs-modules/mailer';
import { MailgunService } from 'src/mail/services/mailgun.service';
import { CallNotificationService } from './call-notification.service';

pubClient.connect();

@WebSocketGateway({
    cors: {
        origin: '*',
    },
    // namespace: 'live-chat',
    // Server: {
    //     adapter: createAdapter(pubClient)
    // }
})

export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {

    constructor(
        private readonly chatService: ChatService,
        private readonly messageService: ChatMessageService,
        private readonly chatUserService: ChatuserService,
        private logService: LogService,
        private messageScheduleService: MessageScheduleService,
        private chatInviteService: ChatInviteService,
        private activityService: ActivityService,
        private redisService: RedisService,
        private rabbitMQService: RabbitMQService,
        private jwtService: JwtService,
        private configService: ConfigService,
        private mailerService: MailgunService,
        private callNotificationService: CallNotificationService

    ) { this.subscribeToRedisChannel(); }
    @WebSocketServer()
    server: Server;
    async afterInit(server: Server) {
        this.logService.log('Socket is live for chat.gateway')

    }

    users = new Map<string, { tenantId: string, jobProUserId: string, socket: Socket }[]>();
    sentSchedules: { id: string, date: Date }[] = [];

    /**
     * Helper method to extract user IDs from chat users array
     */
    private extractUserIds(users: any[]): string[] {
        return users.map(user => {
            if (!user) return '';
            if (typeof user === 'string') return user;
            if (typeof user === 'object') {
                if (user._id) return user._id.toString();
                if (user.id) return user.id.toString();
            }
            return user.toString();
        }).filter(id => id !== '');
    }
    @SubscribeMessage('message')
    onMessage(@MessageBody() body: any, @ConnectedSocket() socket: Socket) {
        this.logService.log('on message: ', body);
        this.server.adapter
        return body;
    }
    async onModuleInit() {
        await this.rabbitMQService.receiveMessage('http-message', (message) => {
            this.logService.log('Received message:', message);
            this.broadcastHttpMessage(message)
        });
    }
    @EventPattern('http-message')
    async handleHttpMessage(data: any): Promise<any> {
        // Process the message here
        this.logService.log('Received http-message 1:', data);
        return { success: true }
    }

    @Cron(CronExpression.EVERY_MINUTE, { timeZone: 'UTC' }) // Run the task every minute
    async broadcastCronMessage() {
        // this.logService.log('Cron job for message schedule');
        try {
            const schedules = await this.messageScheduleService.findDueSchedules();
            const mydate = new Date();
            const utcDate = Date.UTC(
                mydate.getFullYear(),
                mydate.getMonth(),
                mydate.getDate(),
                mydate.getHours(),
                mydate.getMinutes(),
                mydate.getSeconds()
            );
            const today = new Date(utcDate);
            // this.logService.log('UTC Date: ', utcDate)
            schedules?.forEach(async (schedule) => {
                const scheduleDate = schedule.dueDate;
                const msgDate = schedule.dueDate;
                msgDate.setUTCHours(parseInt(schedule.time.split(':')[0]));
                msgDate.setUTCMinutes(parseInt(schedule.time.split(':')[1]));
                // this.logService.log('msg Date time: ', msgDate, schedule);

                const message = new ChatMessageDto();
                message.id = schedule.id;
                message.text = schedule.text;
                message.files = schedule.files;
                message.sender = schedule.userId;
                message.chatId = schedule.chatId;
                message.id = new Types.ObjectId().toString();
                message.createdAt = msgDate;

                if (schedule.files.length && !schedule.text) {
                    message.messageType = MESSAGE_TYPE.TEXT_WITH_FILE;
                }
                if (schedule.files.length && schedule.text) {
                    message.messageType = MESSAGE_TYPE.FILE;
                }
                if (!schedule.files.length && schedule.text) {
                    message.messageType = MESSAGE_TYPE.TEXT;
                }
                const payload: Payload = {
                    event: CHAT_EVENTS.NEW_MESSAGE,
                    data: message
                };

                const targetTime = schedule.time;
                const targetTimeSplit = targetTime.split(":");
                const targetHours = parseInt(targetTimeSplit[0], 10);
                const targetMinutes = parseInt(targetTimeSplit[1], 10);

                const currentHours = today.getUTCHours();
                const currentMinutes = today.getUTCMinutes();


                const timeDifference = (currentHours * 60 + currentMinutes) - (targetHours * 60 + targetMinutes);
                const lockName = schedule.id;
                const ttl = 60; // 30 seconds

                if (today.getDate() == scheduleDate.getDate() && !schedule.isRecurring) {
                    if (Math.abs(timeDifference) > 0 && Math.abs(timeDifference) == 1) {

                        if (await this.redisService.acquireLock(lockName, lockName, ttl)) {
                            try {
                                this.logService.log('about to send msg')
                                this.sentSchedules.push({ id: schedule.id, date: today });
                                this.logService.log('sentSchedule: ', this.sentSchedules);
                                await this.messageScheduleService.deleteSchedule(schedule.id);
                                const saved = await this.messageService.createMessage(message);
                                // this.logService.log('saved message sent: ', saved);

                                this.server.to(schedule.chatId).emit(CHAT_EVENT, payload);

                                this.sendNotificationToAllWithString(schedule.chatId);
                            } finally {
                                await this.redisService.releaseLock(lockName, schedule.id);
                            }
                        } else {
                            this.logService.log('Lock is already acquired, skipping message send');
                        }
                    }


                }


                const currentDayIndex = today.getDay();
                const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const currentDay = daysOfWeek[currentDayIndex];
                if (schedule.isRecurring && schedule.days.includes(currentDay) && (Math.abs(timeDifference) > 0 && Math.abs(timeDifference) <= 1)) {
                    if (await this.redisService.acquireLock(lockName, lockName, ttl)) {
                        try {
                            this.logService.log('about to send msg')
                            this.server.to(schedule.chatId).emit(CHAT_EVENT, payload);
                            const saved = await this.messageService.createMessage(message);
                            //   this.logService.log('saved message sent: ', saved);
                        } finally {
                            await this.redisService.releaseLock(lockName, schedule.id);
                        }
                    } else {
                        this.logService.log('Lock is already acquired, skipping message send');
                    }
                }
            });
        } catch (error) {
            this.logService.error('Error in schedule message cron job: ', error.message)
        }
    }

    @UseGuards(WSAuthGuard)
    async handleConnection(socket: Socket, ...args: any[]) {
        try {

            let { authorization } = socket.handshake.auth;
            if (authorization?.includes(' ')) {
                const array = authorization?.split(' ');
                authorization = array.length > 1 ? array[1] : array[0];
            }
            // this.logService.log('Socket auth : ', authorization);
            const payload = this.jwtService.decode(authorization);

            if (payload) {
                const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
                const tenantId = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid'] || payload['tenantId'];
                const jobProUserId = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
                this.logService.log('jobProUserId: ', jobProUserId, 'tenantId: ', tenantId, ' email: ', email);
                if (tenantId) socket.join(tenantId);
                let user = await this.chatUserService.findUserByJobProId(jobProUserId);
                // this.logService.log('user: ', user);
                if (!user) { user = await this.chatUserService.createUser({ email, tenantId: tenantId, jobProUserId }) }

                // this.logService.log('user 1: ', user);
                const updatedUser = await this.chatUserService.updateStatus(USER_STATUSES.ACTIVE, user.id);
                // this.redisService.setUserSocket(user.id, socket.id);

                socket.to(tenantId).emit(CHAT_EVENT, { event: CHAT_EVENTS.TOGGLE_STATUS, data: updatedUser });
                socket.emit(CHAT_EVENT, { event: CHAT_EVENTS.TOGGLE_STATUS, data: updatedUser });
                socket.join(user.id.trim())
                this.chatService.findChatsByUserId(user.id, tenantId).then(chats => chats.forEach(chat => {
                    this.logService.log('chat id: ', chat.id)
                    socket.join(chat.id.toString());

                }));

                this.getUserChats(socket, tenantId, user);

            }
        } catch (error) {
            this.logService.error('Error in handle connection:', error)
        }

    }
    async handleDisconnect(socket: Socket) {
        try {
            this.logService.log('User disconnected: ', socket.id);
            const user = this.getUserBySocket(socket.id);
            // this.logService.log('disconnecting user: ', user)
            const statusPayload: Payload = {
                data: { userId: user?.userId, status: 'OFFLINE', jobProUserId: user?.jobProUserId, tips: 'oops' },
                event: CHAT_EVENTS.ONLINE_STATUS
            }
            // this.server.to('66cf2c6aea985bcc32f31b19').emit(CHAT_EVENT,statusPayload)
            let { authorization } = socket.handshake.auth;
            if (authorization?.includes(' ')) {
                const array = authorization?.split(' ');
                authorization = array.length > 1 ? array[1] : array[0];
            }
            // this.logService.log('Socket auth : ', authorization);
            const payload = this.jwtService.decode(authorization);
            if (payload) {
                const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
                const tenantId = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid'];
                const jobProUserId = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
                if (tenantId) socket.join(tenantId);
                const user = await this.chatUserService.findUserByJobProId(jobProUserId);
                const updatedUser = await this.chatUserService.updateStatus(USER_STATUSES.OFFLINE, user.id);
                socket.to(tenantId).emit(CHAT_EVENT, { event: CHAT_EVENTS.TOGGLE_STATUS, data: updatedUser });
                socket.emit(CHAT_EVENT, { event: CHAT_EVENTS.TOGGLE_STATUS, data: updatedUser });

                this.logService.log('tenantId: ', tenantId, socket.rooms)
                socket.to(tenantId).emit(CHAT_EVENT, statusPayload);


            }
            this.logService.log('Disconnecting user: ', user);
        } catch (error) {
            this.logService.error('Disconnecting error: ', error)
        }
    }
    async subscribeToRedisChannel() {
        this.logService.log('registering the subscribe function: ', subClient.isOpen);
        if (!subClient.isOpen) { await subClient.connect(); }
        this.logService.log('sub client open status: ', subClient.isOpen);
        subClient.subscribe('chat:created',
            (message, channel) => {
                this.logService.log('redis event: ', channel, message)
                if (channel === 'chat:created') {
                    const { chatId, userIds } = JSON.parse(message);
                    this.logService.log('chatId and userIds ', chatId, userIds)
                    this.handleChatCreated({ chatId, userIds });
                }
            });

    }

    async handleChatCreated({ chatId, userIds }) {
        this.logService.log('new chat event: ', chatId, userIds)
        // Retrieve socket IDs from Redis for each member
        // const socketIds = await Promise.all(members.map((member) => this.redisService.getMemberSocket(`socket:${member}`)));

        // Join sockets to the chat room
        const chat = await this.chatService.findChatById(chatId);
        if (chat) {
            const chatPaylod: Payload = {
                event: CHAT_EVENTS.CREATE_CHAT,
                data: chat
            }
            userIds.forEach(async (userId: string) => {
                this.server.in(userId.trim()).socketsJoin(chatId);
                this.server.to(userId).emit(CHAT_EVENT, chatPaylod);

            });
        }
    }


    @UseGuards(WSAuthGuard)
    @SubscribeMessage(CHAT_EVENT)
    handleEvent(@MessageBody() payload: Payload, @ConnectedSocket() socket: Socket) {
        console.log('Received payload: ', payload);
        // this.logService.log('event payload from client: ', payload);
        this.logService.log('socket user: ', socket['user']);
        const user = socket['user'] as ChatUser;
        this.logService.log('socket user: ', user)
        const tenantId = socket['tenantId'] as string;
        const jobProUserId = socket['jobProUserId'];
        console.log('tenantId: ', tenantId, ' jobProUserId: ', jobProUserId);

        //  this.logService.log('users in event: ', this.users);

        console.log('socket rooms: ', user);
        this.checkUserSocket(socket, user.id, tenantId, jobProUserId)

        //  this.logService.log('users after check: ', this.users);
        if (!socket.rooms.has(user.id.toString())) {
            socket.join(user.id.toString());
        }

        switch (payload.event) {
            case CHAT_EVENTS.GET_CHAT_PAGINATED_MESSAGES:
                this.getChatMessages(payload, socket);
                break;
            case CHAT_EVENTS.GET_CHATS_WITH_UNREAD_COUNT:
                this.getUserChats(socket, tenantId, user);
                break;
            case CHAT_EVENTS.NEW_MESSAGE:
                this.createNewMessage(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.UPDATE_MESSAGE:
                this.updateMessage(payload, socket);
                break;
            case CHAT_EVENTS.DELETE_MESSAGE:
                this.deleteMessage(payload, socket, user);
                break;
            case CHAT_EVENTS.PIN_UNPIN_MESSAGE:
                this.pinUnPinMessage(payload, socket, user)
                break;
            case CHAT_EVENTS.PIN_CHAT:
                this.pinUnpinChat(payload, socket, user);
                break;
            case CHAT_EVENTS.UPDATE_READ_STATUS:
                this.updateReadStatus(payload, socket, user);
                break;
            case CHAT_EVENTS.GET_ALL_UNREAD_MESSAGES:
                this.getAllUnReadMessages(payload, socket, tenantId, user);
                break;
            case CHAT_EVENTS.ADD_REACTION_TO_MESSAGE:
                this.addReaction(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.REMOVE_REACTION_FROM_MESSAGE:
                this.removeReaction(payload, socket, user)
                break;
            case CHAT_EVENTS.CREATE_CHAT:
                this.createChat(payload, socket, tenantId, user);
                break;
            case CHAT_EVENTS.UPDATE_CHAT:
                this.updateChat(payload, socket, user)
                break;
            case CHAT_EVENTS.IS_RECORDING:
                this.isRecording(payload, socket);
                break;
            case CHAT_EVENTS.JOIN_CIRCLE:
                this.joinCircle(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.ADD_USERS_TO_CHAT:
                this.addUsersToChat(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.LEAVE_CHAT:
                this.leaveChat(payload, socket, user, tenantId)
            case CHAT_EVENTS.REMOVE_USER_FROM_CHAT:
                this.removeUserFromChat(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.IS_TYPING:
                this.isTyping(payload, socket);
                break;

            case CHAT_EVENTS.INITIATE_CALL:
                this.initiateCall(payload, socket);
                break;
            case CHAT_EVENTS.ANSwER_CALL:
                this.answerCall(payload, socket, user);
                break;
            case CHAT_EVENTS.DECLINE_CALL:
                this.declineCall(payload, socket, user);
                break;
            case CHAT_EVENTS.END_CALL:
                this.endCall(payload, socket);
                break;
            case CHAT_EVENTS.GET_USER_DATA:
                this.getUserData(payload, socket, tenantId, user);
                break;
            case CHAT_EVENTS.CALL_RINGING:
                this.handleCallRinging(payload, socket)
                break;

            case CHAT_EVENTS.MISSED_CALL:
                this.handleMissCall(payload, socket,);
                break;
            case CHAT_EVENTS.UPDATE_COLOR_PREFERENCE:
                this.updateUserColorPreference(payload, socket)
                break;
            case CHAT_EVENTS.CALL_CANCELED:
                this.cancelCall(payload, socket, user);
                break;
            case CHAT_EVENTS.CHANGE_GROUP_TO_CIRCLE:
                this.handleChangeGroupToCircle(payload, socket);
                break;
            case CHAT_EVENTS.TOGGLE_STATUS:
                this.handleToggleStatus(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.CHAT_CALL:
                this.handleChatCall(payload, socket, user, tenantId);
                break;
            case CHAT_EVENTS.BUSY:
                this.isBusy(payload, socket, user);
                break;
            case CHAT_EVENTS.IGNORE_CALL:
                this.ignoreCall(payload, socket);
                break;
            case CHAT_EVENTS.MARK_ALL_AS_READ:
                this.markAllAsRead(payload, socket, user, tenantId);
                break;
            default:
                payload.error = 'Invalid event type'
                socket.emit(CHAT_EVENT, payload);
        }
    }
    async markAllAsRead(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const { chatId } = payload.data;
            const res = await this.messageService.markAllAsRead(chatId, user.id.toString());
            payload.data = { ...res, chatId };

            this.server.to(user.id.toString()).emit(CHAT_EVENT, payload)
        } catch (error) {
            this.logService.error('Error marking all as read: ', error.message)
        }
    }

    async handleChatCall(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        const chatData = payload.data as ChatDto;
        chatData.tenantId = tenantId;
        const chat = await this.chatService.createChat(chatData);
        payload.data = chat;
        // this.logService.log('chat: ', chat);
        const userIds = this.extractUserIds(chat.users);
        socket.join(chat.id.toString());
        this.logService.log('created chat userIds: ', userIds)
        // this.logService.log('Users with Socket: ',this.users)
        for (let i = 0; i < userIds.length; i++) {
            const userSockets = this.users.get(userIds[i]);
            this.logService.log('found sockets: ', userSockets)
            userSockets?.forEach(data => {
                data.socket.join(chat.id.toString());
                // this.server.sockets.[data.socket.id]
            })
        }
        socket.to(chat.id.toString()).emit(CHAT_EVENT, payload)
        socket.emit(CHAT_EVENT, payload);

        // create a chat chat call object and send to the receiver socket

    }
    async handleToggleStatus(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        const { status } = payload.data;
        const updatedUser = await this.chatUserService.updateStatus(status, user.id);
        // this.logService.log('user status: ', updatedUser)
        payload.data = updatedUser;
        socket.to(tenantId).emit(CHAT_EVENT, payload);
        socket.emit(CHAT_EVENT, payload);
    }

    async handleChangeGroupToCircle(payload: Payload, socket: Socket) {
        const { name, chatId, isPrivate, newMembersJobProIds } = payload.data;
        const updatedChat = await this.chatService.changeGroupChatToCircle(chatId, name, isPrivate, newMembersJobProIds);
        payload.data = updatedChat;
        socket.to(chatId).emit(CHAT_EVENT, payload);
        socket.emit(CHAT_EVENT, payload);
    }

    async leaveChat(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const { chatId, jobProUserId, name, username } = payload.data;
            const userToRemove = await this.chatUserService.findUserByJobProId(jobProUserId);
            const updateRes = await this.chatService.removeUserFromChat(chatId, jobProUserId, name, user.id);
            payload.data = updateRes;
            socket.to(chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);

            const saved = await this.messageService.createMessage({ sender: user.id, addedJobProUserIds: [user.jobProUserId], text: 'This user has left the circle', chatId, isSystemGenerated: true, senderJobProUserId: user.jobProUserId })

            const leftMessagePayload: Payload = {
                event: CHAT_EVENTS.NEW_MESSAGE,
                data: saved
            }
            socket.to(chatId.toString()).emit(CHAT_EVENT, leftMessagePayload);
            socket.emit(CHAT_EVENT, leftMessagePayload);


            const activityObj: ChatActivityDto = {
                sender: user.id,
                chat: chatId,
                activityType: ACTIVITY_TYPE.INVITE,
                inviteType: INVITE_TYPE.LEFT,
                user: user.id,
                tenantId: tenantId
            }
            const activity = await this.activityService.createActivity(activityObj);

            const activityPayload: Payload = {
                event: CHAT_EVENTS.NEW_ACTIVITY,
                data: activity
            };
            socket.emit(CHAT_EVENT, activityPayload);

        } catch (error) {
            this.logService.error('Error removing user from chat: ', error);
            payload.error = 'Error leaving user from chat: ' + error;
            socket.emit(CHAT_EVENT, payload)
        }
    }

    async joinCircle(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const { chatId, username } = payload.data;
            this.logService.log('joining a circle: ', chatId, username);
            const hasJoined = await this.chatService.checkUserIsAChatMember(user.jobProUserId, chatId);
            if (hasJoined) {
                return;
            }
            const chat = await this.chatService.findChatById(chatId);
            await this.chatService.addUserToChat(user.jobProUserId, chatId, user.email, chat);

            const saved = await this.messageService.createMessage({ sender: user.id, senderJobProUserId: user.jobProUserId, addedJobProUserIds: [user.jobProUserId], text: 'This user has joined the circle', chatId, isSystemGenerated: true })

            payload.data = saved.toJSON() //await this.messageService.findMessageById(saved.id);
            payload.event = CHAT_EVENTS.NEW_MESSAGE
            this.logService.log('joined message: ', saved)
            socket.join(chatId);

            const updatedChat = await this.chatService.findChatByIdPopulated(chatId);
            // this.logService.log('updated chat: ', updatedChat);
            const chatPaylod: Payload = {
                event: CHAT_EVENTS.JOIN_CIRCLE,
                data: updatedChat
            }

            socket.to(chatId).emit(CHAT_EVENT, chatPaylod);
            socket.emit(CHAT_EVENT, chatPaylod);


            setTimeout(() => {
                socket.to(chatId).emit(CHAT_EVENT, payload);
                socket.emit(CHAT_EVENT, payload);
            }, 1000);
            const activityObj: ChatActivityDto = {
                sender: user.id,
                chat: chatId,
                activityType: ACTIVITY_TYPE.INVITE,
                inviteType: INVITE_TYPE.JOINED,
                user: user.id,
                tenantId
            }

            const activity = await this.activityService.createActivity(activityObj);

            const activityPayload: Payload = {
                event: CHAT_EVENTS.NEW_ACTIVITY,
                data: activity
            };
            socket.emit(CHAT_EVENT, activityPayload)

        } catch (error) {
            this.logService.error('error: ', error)
            payload.error = error;
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async updateUserColorPreference(payload: Payload, socket: Socket) {
        try {
            const userData = new UpdateChatUserDto();
            userData.id = payload.data.id,
                userData.jobProUserId = payload.data.jobProUserId;
            userData.colorPreference = payload.data.colorPreference;
            const up = await this.chatUserService.updateUserColorPreference(userData);
            this.logService.log('color pref update: ', up);
            payload.data = up;
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error updating color preference: ', error)
        }
    }
    async handleCallRinging(payload: Payload, socket: Socket) {
        const callData = payload.data as ChatCallDto;
        callData.status = CHAT_CALL_STATUS.RINGING
        socket.to(callData.callerSocket).emit(CHAT_EVENT, payload)

    }

    async handleMissCall(payload: Payload, socket: Socket) {
        const callData = payload.data as ChatCallDto;
        callData.status = CHAT_CALL_STATUS.MISSED;
        const chat = await this.chatService.findChatById(callData.chatId.toString());
        if (chat.chatType == CHAT_TYPE.DM) {
            this.messageService.updateCallMessage(callData.callId, { callStatus: CHAT_CALL_STATUS.MISSED }).then(updated => {
                this.server.to(callData.meetingId).emit(CHAT_EVENT, { event: CHAT_EVENTS.UPDATE_MESSAGE, data: updated });
            });
        }
        socket.to(callData.callerSocket).emit(CHAT_EVENT, payload);

        // Send missed call push notifications
        try {
            const caller = await this.chatUserService.findUser(callData.callerId);
            if (caller && chat) {
                const chatUserIds = this.extractUserIds(chat.users);
                const offlineUserIds = await this.callNotificationService.getOfflineUsers(
                    chatUserIds,
                    callData.meetingId || callData.chatId,
                    this.server
                );

                if (offlineUserIds.length > 0) {
                    await this.callNotificationService.sendMissedCallNotification(
                        callData,
                        caller,
                        offlineUserIds,
                        socket
                    );
                }
            }
        } catch (error) {
            this.logService.error('Error sending missed call notifications: ', error);
        }
    }
    async getUserData(payload: Payload, socket: Socket, tenantId: string, userData: ChatUser) {
        try {
            this.logService.log('Get user data: ', payload);
            
            // Get the latest user data from database to ensure we have the most up-to-date information
            let user = await this.chatUserService.findUserByJobProId(userData.jobProUserId);
            if (!user) {
                const { email } = payload.data;
                user = await this.chatUserService.createUser({ jobProUserId: userData.jobProUserId, tenantId, email });
            }
            
            this.logService.log('user data: ', user);
            payload.data = user;
            socket.emit(CHAT_EVENT, payload);

        } catch (error) {
            this.logService.error('Error getting chat user: ', error);
            payload.error = error;
            socket.emit(CHAT_EVENT, payload);
        }
    }
    async createChat(payload: Payload, socket: Socket, tenantId: string, user: ChatUser) {
        try {
            const chatData = payload.data as ChatDto;
            chatData.tenantId = tenantId;
            chatData.createdBy = user.id;
            this.logService.log('chat data: ', chatData, payload)
            chatData.jobProUserIds.push(user.jobProUserId);

            if (!chatData.createdBy) {
                payload.error = 'Error creating chat: ' + 'createdBy field is required!'
                socket.emit(CHAT_EVENT, payload)
                return;
            }
            
            chatData.tenantId = tenantId;
            const chat = await this.chatService.createChat(chatData);
            payload.data = chat;
            // this.logService.log('chat: ', chat);
            const userIds = this.extractUserIds(chat.users);
            if (this.redisService.redisClient.isOpen) {
                this.logService.log('redis is open')
                this.redisService.redisClient.publish('chat:created', JSON.stringify({ chatId: chat.id, userIds })).then((result) => this.logService.log('REDIS PUBLISH RESULT:', result)).catch(err => {
                    socket.join(chat.id.toString());

                    this.server.to(chat.id.toString()).emit(CHAT_EVENT, payload);
                });
            } else {
                this.logService.log('redis is not open');
                await this.redisService.redisClient.connect();
                if (this.redisService.redisClient.isOpen) {
                    this.redisService.redisClient.publish('chat:created', JSON.stringify({ chatId: chat.id, userIds })).then((result) => this.logService.log('REDIS PUBLISH RESULT:', result)).catch(err => {
                        socket.join(chat.id.toString());
                        this.server.to(chat.id.toString()).emit(CHAT_EVENT, payload);
                    });
                }
            }
            // socket.emit(CHAT_EVENT, payload);

            userIds.map(async (userId) => {
                const foundUser = await this.chatUserService.findUser(userId);
                if (userId != user.id)
                    this.sendNotification(userId, chat.id, tenantId, chat?.name ?? '', user, `You have been added to a chat: ${chat.name} in Joble`, null, 'You are added to a chat in Joble', null, user);

                const origin = socket.handshake.headers.origin;
                const hostname = new URL(origin).hostname;
                const subdomain = hostname.split('.')[0];

                this.logService.log('firstSubdomain ', subdomain);
                const html = this.mailerService.mentionHTML(subdomain, chat.name, chat.id, `You have being added to a ${chat.chatType} chat in ${subdomain} workspace on Joble`);

                this.sendEmailNotification(userId, `New ${chat.chatType} Chat in JobChat`, user, html, socket);
                //this.mailerService.sendMail({ to: [foundUser.email], html, subject: `New ${chat.chatType} Chat in JobChat', sender: 'Joble` });
            })

            const message = await this.messageService.createMessage({ chatId: chat.id, sender: chat.createdBy['id'], senderJobProUserId: chat.createdBy['jobProUserId'], text: 'This is the begining of chat messages between you and other participants in the chat.', isSystemGenerated: true, isFirstChatMessage: true, addedJobProUserIds: [chat.createdBy['id']] });

            const defaultMessagePayload: Payload = {
                event: CHAT_EVENTS.NEW_MESSAGE,
                data: message
            }

            this.server.to(chat.id.toString()).emit(CHAT_EVENT, defaultMessagePayload);

        } catch (error) {
            this.logService.error('Error creating chat: ', error);
            payload.error = 'Error creating chat: ' + error.message
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async updateChat(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            const { chatId, update } = payload.data;
            const updateRes = await this.chatService.updateChat(chatId, user.id, update);
            payload.data = updateRes;
            socket.to(chatId).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error updating chat');
            payload.error = error.message;
            socket.emit(CHAT_EVENT, payload)
        }
    }
    isRecording(payload: Payload, socket: Socket) {
        const { chatId, userId, isRecording, message } = payload.data;
        socket.to(chatId).emit(CHAT_EVENT, payload)
        socket.emit(CHAT_EVENT, payload);
    }
    async addUsersToChat(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const { jobProUserIds, emails, chatId, name, addedUsersNames, addedByUserName } = payload.data;
            socket.join(chatId);
            this.logService.log('chat new name:', name);
            const res = await this.chatService.addUsersToChat(jobProUserIds, emails, chatId, name);
            payload.data = res;
            for (let i = 0; i < res.users.length; i++) {
                const userSockets = this.users.get(res.users[i]);
                this.logService.log('user sockets: ', userSockets)
                userSockets?.forEach(data => data.socket.join(chatId));
            }
            socket.to(chatId).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);

            for (let i = 0; i < jobProUserIds.length; i++) {
                const foundUser = await this.chatUserService.findUserByJobProId(jobProUserIds[i]);
                const activityObj: ChatActivityDto = {
                    sender: user.id,
                    chat: chatId,
                    tenantId: tenantId,
                    activityType: ACTIVITY_TYPE.INVITE,
                    inviteType: INVITE_TYPE.ADDED,
                    user: foundUser.id.toString() ?? ''
                }

                const activity = await this.activityService.createActivity(activityObj);
                const activityPayload: Payload = {
                    event: CHAT_EVENTS.NEW_ACTIVITY,
                    data: activity
                };
                socket.to(foundUser.id.toString()).emit(CHAT_EVENT, activityPayload);
                const origin = socket.handshake.headers.origin;
                const hostname = new URL(origin).hostname;
                const subdomain = hostname.split('.')[0];
                this.sendNotification(foundUser.id, chatId, tenantId, name ?? '', user, `You have being added to ${name} Chat  in ${subdomain} workspace on Joble`, null, 'Chat addition', null, user);

                const html = this.mailerService.mentionHTML(subdomain, name, chatId, `You have being added to chat in ${subdomain} workspace on Joble`);

                this.mailerService.sendMail({ to: [foundUser.email], html, subject: `You have being added to ${name} Chat on Joble', sender: 'Joble` });

            }
            if (addedByUserName && addedUsersNames.length) {
                let text = addedByUserName + ' added ';
                if (addedUsersNames.length > 3) {
                    text += addedUsersNames[0] + ', ' + addedUsersNames[1] + ', ' + addedUsersNames[2] + ' and ' + (parseInt(addedUsersNames.length) - 3) + ' others';
                } else {
                    text += addedUsersNames.join(', ')
                }
                text += ' to ' + name + ' chat';

                const messageDto: ChatMessageDto = {
                    text: text,
                    sender: user.id, senderJobProUserId: user.jobProUserId,
                    isSystemGenerated: true,
                    chatId: chatId
                }
                const message = await this.messageService.createMessage(messageDto);
                const messagePayload: Payload = {
                    event: CHAT_EVENTS.NEW_MESSAGE,
                    data: message
                }
                socket.to(chatId).emit(CHAT_EVENT, messagePayload);
                socket.emit(CHAT_EVENT, messagePayload);
            }
        } catch (error) {
            this.logService.error('Error add users to chat: ', error);
            payload.error = 'Error add users to chat: ' + error;
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async removeUserFromChat(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const { chatId, jobProUserId, name, removedUsersNames, removedByUserName } = payload.data;
            socket.join(chatId);
            const foundUser = await this.chatUserService.findUserByJobProId(jobProUserId);
            const updateRes = await this.chatService.removeUserFromChat(chatId, jobProUserId, name, foundUser.id);
            payload.data = updateRes;
            socket.to(chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);

            const activityObj: ChatActivityDto = {
                sender: foundUser.id,
                chat: chatId,
                tenantId: tenantId,
                activityType: ACTIVITY_TYPE.INVITE,
                inviteType: INVITE_TYPE.REMOVED,
                user: foundUser.id ?? ''
            }
            const activity = await this.activityService.createActivity(activityObj);

            const activityPayload: Payload = {
                event: CHAT_EVENTS.NEW_ACTIVITY,
                data: activity
            };
            socket.to(foundUser.toString()).emit(CHAT_EVENT, activityPayload);

            if (removedByUserName && removedUsersNames?.length) {
                for (const removedUser of removedUsersNames) {
                    const messageDto: ChatMessageDto = {
                        text: removedByUserName + ' removed ' + removedUser + ' from ' + name + ' chat',
                        sender: user.id,
                        senderJobProUserId: user.jobProUserId,
                        isSystemGenerated: true,
                        chatId: chatId
                    }
                    const message = await this.messageService.createMessage(messageDto);
                    const messagePayload: Payload = {
                        event: CHAT_EVENTS.NEW_MESSAGE,
                        data: message
                    }
                    socket.to(chatId).emit(CHAT_EVENT, messagePayload);
                    socket.emit(CHAT_EVENT, payload);
                }
            }

            if (user.jobProUserId != jobProUserId) {
                const origin = socket.handshake.headers.origin;
                const hostname = new URL(origin).hostname;
                const subdomain = hostname.split('.')[0];
                this.sendNotification(foundUser.id, chatId, tenantId, name ?? '', user, `You have being removed from ${name} Chat  in ${subdomain} workspace on Joble`, null, 'You have been removed from a chat in Joble', null, user);

                const html = this.mailerService.mentionHTML(subdomain, name, chatId, `You have being removed from chat in ${subdomain} workspace on Joble`);

                this.mailerService.sendMail({ to: [foundUser.email], html, subject: `You have being removed from ${name} Chat on Joble', sender: 'Joble` });
            } else {
                const origin = socket.handshake.headers.origin;
                const hostname = new URL(origin).hostname;
                const subdomain = hostname.split('.')[0];

                this.sendNotification(foundUser.id, chatId, tenantId, name ?? '', user, `You have being removed from ${name} Chat in ${subdomain} workspace on Joble`, null, 'You have left a chat in Joble', null, user);


                const html = this.mailerService.mentionHTML(subdomain, name, chatId, `You left ${name} chat in ${subdomain} workspace on Joble`);

                this.mailerService.sendMail({ to: [foundUser.email], html, subject: `You have being removed from ${name} Chat on Joble', sender: 'Joble` });
            }

        } catch (error) {
            this.logService.error('Error removing user from chat: ', error);
            payload.error = 'Error removing user from chat: ' + error;
            socket.emit(CHAT_EVENT, payload)
        }
    }
    isTyping(payload: Payload, socket: Socket) {
        const { chatId } = payload.data
        socket.to(chatId).emit(CHAT_EVENT, payload);
    }
    async initiateCall(payload: Payload, socket: Socket) {
        // socket.join('66965288e4b1b22a92690ed9');
        const callData = payload.data as ChatCallDto;

        this.logService.log('call payload: ', payload.data)
        // const callId = v4();
        // payload.data.callId = callId;
        socket.to(callData.meetingId).emit(CHAT_EVENT, payload)
        socket.emit(CHAT_EVENT, payload);
        // const call = this.callService.create(payload.data);
        this.logService.log('call saved: ', payload);
        const sender = await this.chatUserService.findUser(callData.callerId);
        const message: ChatMessageDto = {
            sender: sender.id,
            senderJobProUserId: sender.jobProUserId,
            callType: callData.format,
            chatId: callData.meetingId,
            messageType: MESSAGE_TYPE.CALL,
            callStatus: CHAT_CALL_STATUS.IN_PROGRESS,
            callId: callData.callId
        }
        const savedCall = await this.messageService.createCallMessage(message);
        this.logService.log('call msg: ', savedCall);

        this.server.to(callData.meetingId).emit(CHAT_EVENT, { data: savedCall, event: CHAT_EVENTS.NEW_MESSAGE });

        // Send push notifications to offline users
        try {
            const chat = await this.chatService.findChatById(callData.meetingId);

            if (chat) {
                const chatUserIds = this.extractUserIds(chat.users);
                const offlineUserIds = await this.callNotificationService.getOfflineUsers(
                    chatUserIds,
                    callData.meetingId,
                    this.server
                );

                if (offlineUserIds.length > 0) {
                    await this.callNotificationService.sendIncomingCallNotification(
                        callData,
                        sender,
                        offlineUserIds,
                        socket
                    );
                }
            }
        } catch (error) {
            this.logService.error('Error sending call notifications: ', error);
        }

        return payload;
    }
    async isBusy(payload: Payload, socket: Socket, user: ChatUser) {
        const { callerSocket, message } = payload.data
        socket.to(callerSocket).emit(CHAT_EVENT, payload)
    }
    async ignoreCall(payload: Payload, socket: Socket) {
        const callData = payload.data as ChatCallDto;
        socket.to(callData.callerSocket).emit(CHAT_EVENT, payload)

    }
    async answerCall(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            const callData = payload.data as ChatCallDto;
            const sender = await this.chatUserService.findUser(callData.callerId);
            this.logService.log('answer call data: ', callData)
            const message: ChatMessageDto = {
                sender: sender.id,
                senderJobProUserId: sender.jobProUserId,
                callType: callData.format,
                chatId: callData.meetingId,
                messageType: MESSAGE_TYPE.CALL,
                callStatus: 'In-Progress',
                callId: callData.callId
            }
            const savedCall = await this.messageService.createCallMessage(message);
            this.logService.log('call message: ', savedCall)
            if (!savedCall['alreadyExist']) {
                payload.data = savedCall;
                payload.event = CHAT_EVENTS.NEW_MESSAGE
                socket.to(callData.meetingId).emit(CHAT_EVENT, payload);
                socket.emit(CHAT_EVENT, payload);

                // const joinedPayload: Payload = {
                //     event: CHAT_EVENTS.JOINED_CALL,
                //     data: { joinedSocket: socket.id }
                // }
                // socket.emit(CHAT_EVENT, joinedPayload)
            }
            const joinedUserPayload: Payload = {
                event: CHAT_EVENTS.JOINED_CALL,
                data: { socketId: socket.id, userId: user.id.toString(), message: 'You have joined the call on one of your devices with the socketId:' + socket.id }
            }
            socket.to(user.id.toString()).emit(CHAT_EVENT, joinedUserPayload);

            this.server.to(callData.meetingId).emit(CHAT_EVENT, { event: CHAT_EVENTS.ANSwER_CALL, data: callData });

            // Send call answered push notification to caller if they're offline
            try {
                if (sender && sender.id !== user.id) {
                    const isCallerOnline = await this.callNotificationService.getOfflineUsers(
                        [sender.id],
                        callData.meetingId,
                        this.server
                    );

                    if (isCallerOnline.length > 0) {
                        await this.callNotificationService.sendCallAnsweredNotification(
                            callData,
                            user,
                            sender.id,
                            socket
                        );
                    }
                }
            } catch (error) {
                this.logService.error('Error sending call answered notification: ', error);
            }
        } catch (error) {
            this.logService.error('Error creating call message: ', error);
            payload.error = 'Error creating call message: ' + error;
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async cancelCall(payload: Payload, socket: Socket, user: ChatUser) {
        this.logService.log('call is being cancelled!')
        const callData = payload.data as ChatCallDto;
        // const chat = await this.chatService.findChatById(callData.chatId.toString());
        // if (chat.chatType == CHAT_TYPE.DM) {
        socket.join(callData.meetingId);
        callData.status = CHAT_CALL_STATUS.CANCELLED
        this.messageService.updateCallMessage(callData.callId, {
            callStatus:
                CHAT_CALL_STATUS.CANCELLED.toString()
        })

        this.server.to(callData.meetingId).emit(CHAT_EVENT, payload)
        // }
        // socket.emit(CHAT_EVENT, payload);
        this.logService.log('call is cancelled');

        // Send call cancelled push notifications to offline users
        try {
            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            if (chat) {
                const chatUserIds = this.extractUserIds(chat.users);
                const offlineUserIds = await this.callNotificationService.getOfflineUsers(
                    chatUserIds,
                    callData.meetingId || callData.chatId,
                    this.server
                );

                if (offlineUserIds.length > 0) {
                    await this.callNotificationService.sendCallCancelledNotification(
                        callData,
                        user,
                        offlineUserIds,
                        socket
                    );
                }
            }
        } catch (error) {
            this.logService.error('Error sending call cancelled notifications: ', error);
        }
    }

    async declineCall(payload: Payload, socket: Socket, user: ChatUser) {
        const callData = payload.data as ChatCallDto;
        callData.status = CHAT_CALL_STATUS.DECLINED;
        this.messageService.updateCallMessage(callData.callId, {
            callStatus:
                CHAT_CALL_STATUS.DECLINED.toString()
        })
        socket.to(callData.callerId).emit(CHAT_EVENT, payload);
        socket.emit(CHAT_EVENT, payload);

        // Send call declined push notification to caller if they're offline
        try {
            const isCallerOnline = await this.callNotificationService.getOfflineUsers(
                [callData.callerId],
                callData.meetingId || callData.chatId,
                this.server
            );

            if (isCallerOnline.length > 0) {
                await this.callNotificationService.sendCallDeclinedNotification(
                    callData,
                    user,
                    callData.callerId,
                    socket
                );
            }
        } catch (error) {
            this.logService.error('Error sending call declined notification: ', error);
        }
    }
    async endCall(payload: Payload, socket: Socket) {
        this.logService.log('End call event: ', payload);
        const data = payload.data;
        const updatedMessage = await this.messageService.updateCallMessage(data.callId, { callStatus: 'Ended' });
        payload.data = updatedMessage;
        payload.event = CHAT_EVENTS.UPDATE_MESSAGE;
        this.logService.log('rooms: ', socket.rooms);
        socket.to(data.meetingId.toString().trim()).emit(CHAT_EVENT, payload);
        socket.emit(CHAT_EVENT, payload);
        this.server.to(data.meetingId.toString().trim()).emit(CHAT_EVENT, { event: CHAT_EVENTS.END_CALL, data: data });

        // Send call ended push notifications to offline users
        try {
            const chat = await this.chatService.findChatById(data.meetingId || data.chatId);
            if (chat && data.callerId) {
                const caller = await this.chatUserService.findUser(data.callerId);
                if (caller) {
                    const chatUserIds = this.extractUserIds(chat.users);
                    const offlineUserIds = await this.callNotificationService.getOfflineUsers(
                        chatUserIds,
                        data.meetingId || data.chatId,
                        this.server
                    );

                    if (offlineUserIds.length > 0) {
                        const callData: ChatCallDto = {
                            callId: data.callId,
                            meetingId: data.meetingId,
                            chatId: data.chatId,
                            callerId: data.callerId,
                            format: data.format || CHAT_CALL_FORMAT.VIDEO,
                            ...data
                        };

                        await this.callNotificationService.sendCallEndedNotification(
                            callData,
                            caller,
                            offlineUserIds,
                            socket
                        );
                    }
                }
            }
        } catch (error) {
            this.logService.error('Error sending call ended notifications: ', error);
        }
    }

    async createNewMessage(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            const id = new Types.ObjectId();
            this.logService.log('id: ', id)

            const data = payload.data as ChatMessageDto;
            // socket.join(data.chatId);
            if (!data.text && !data.files.length) {
                throw new BadRequestException('You can not send an empty message.');
            }
            data.id = id.toString();
            data._id = id;
            data.createdAt = new Date();
            payload.data = data;
            if (!data.senderJobProUserId) {
                data.senderJobProUserId = user.jobProUserId;
            }
            this.logService.log('message data: ', payload.data.chatId, user.tenantId);
            // this.server.to(data.chatId.toString().trim()).emit(CHAT_EVENT, payload);

            setTimeout(() => {
                this.server.to(data.chatId).emit(CHAT_EVENT, payload);
                this.logService.log('Broadcasting to room: ', data.chatId);
            }, 100);

            // this.server.to(user.tenantId).emit(CHAT_EVENT, payload);
            // this.server.emit(CHAT_EVENT, payload);
            // socket.emit(CHAT_EVENT, payload);
            data.sender = user.id;
            if (data.parentId == '' || data.parentId == null) {
                delete data.parentId;
            }

            const res = await Promise.all([
                this.messageService.createMessage(data), this.chatService.findChatByIdPopulated(data.chatId)]);


            // this.logService.log('saved message: ', res)
            // socket.to(res[0].chatId.toString()).emit(CHAT_EVENT, payload);
            // socket.emit(CHAT_EVENT, payload);
            this.logService.log('mentions: ', data.mentions);
            for (let i = 0; i < data.mentions.length; i++) {
                const foundUser = await this.chatUserService.findUserByJobProId(data.mentions[i]);
                const activityObj: ChatActivityDto = {
                    sender: user.id,
                    chat: res[0].chatId,
                    activityType: ACTIVITY_TYPE.MENTION,
                    message: res[0].id,
                    tenantId: tenantId,
                    user: foundUser.id ?? ''
                }
                const sockets = this.getUserSocketIds(foundUser.id);
                const activity = await this.activityService.createActivity(activityObj);

                this.logService.log('Activity: ', activity, data.mentions[i]);
                const activityPayload: Payload = {
                    event: CHAT_EVENTS.NEW_ACTIVITY,
                    data: activity
                };
                socket.to(foundUser.id.trim()).emit(CHAT_EVENT, activityPayload);

                // socket.to(sockets).emit(CHAT_EVENT, activityPayload);
                this.sendNotification(foundUser.id, res[0].chatId, tenantId, res[1].name, foundUser, 'You are mentioned in a chat message in ' + res[1].name, socket, 'Your mentioned in a chat on Joble', null, user);
                res[1].lastMessageTimestamp = Date.now();

                const origin = socket.handshake.headers.origin;
                const hostname = new URL(origin).hostname;
                const subdomain = hostname.split('.')[0];

                const html = this.mailerService.mentionHTML(subdomain, res[1].name, res[1].id);
                this.mailerService.sendMail({ to: [foundUser.email], html, subject: 'New Mention in JobChat', sender: 'Joble' });

                await this.chatService.updateChat(data.chatId, data.sender, { lastMessageTimestamp: Date.now() });
            }
            if (!data.mentions.length) {
                debugger
                this.sendNotificationToAll(res[1], socket, data, user)
            }
            // handle sending notification to users
            // const users = 
        } catch (error) {
            this.logService.error('Error creating message: ', error);
            payload.error = { message: 'Error creating message: ', error: { ...error, fID: payload.data?.fID } };
            // socket.emit(CHAT_EVENT, payload)
        }
    }

    broadcastHttpMessage(message: any) {
        // this.logService.log('http message event: ', message);
        const payload: Payload = {
            event: CHAT_EVENTS.NEW_MESSAGE,
            data: message as ChatMessageDto
        }
        this.logService.log('broadcasting http message: ', payload);
        const clientsInRoom = this.server.sockets.adapter.rooms.get(message.chatId);
        this.logService.log('Clients in room:', clientsInRoom);

        try {
            this.server.to(message.chatId).emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Broadcast error:', error);
        }

    }
    async updateMessage(payload: Payload, socket: Socket) {
        try {

            const update = payload.data;
            if (!update.id) {
                socket.emit(CHAT_EVENT, { ...payload, error: { status: 409, message: 'message id is required' } });
                return;
            }
            const res = await this.messageService.updateMessage(update.id, update);
            payload.data = res;
            socket.to(update.chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error message error: ', error);
            payload.error = 'Error message error: ' + error;
            socket.emit(CHAT_EVENT, payload);
        }
    }
    async deleteMessage(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            const { messageId, chatId } = payload.data;
            this.logService.log('chat id for delete: ', chatId)
            const deleteRes = await this.messageService.deleteMessage(messageId);
            payload.data = { message: deleteRes, sender: user.id };
            // this.logService.log('deleteRes: ', deleteRes);
            socket.to(deleteRes.chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error deleting message: ', error);
            payload.error = 'Error deleting message: ' + error
            socket.emit(CHAT_EVENT, payload);
        }

    }

    async pinUnPinMessage(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            this.logService.log('starting pin unpin message')
            const { messageId, isPinned, chatId } = payload.data;
            const pinRes = await this.messageService.pinAndUnPinMessage(user.id, messageId, isPinned)
            payload.data = pinRes;
            // this.logService.log('pinRes: ', pinRes)
            socket.to(pinRes.chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error pinning un-pinning message: ', error)
            payload.error = 'Error pinning un-pinning message :' + error
            socket.emit(CHAT_EVENT, payload);
        }
    }

    async pinUnpinChat(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            this.logService.log('starting pin unpin chat');
            const { chatId, isPinned } = payload.data;
            const pinRes = await this.chatService.pinUnpinChat(chatId, user.id, isPinned);
            payload.data = pinRes;

            // Emit to all users in the chat
            socket.to(chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error pinning/unpinning chat: ', error);
            payload.error = 'Error pinning/unpinning chat: ' + error;
            socket.emit(CHAT_EVENT, payload);
        }
    }

    async updateReadStatus(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            const { messageId } = payload.data;
            const upRes = await this.messageService.updateReadStatus(messageId, user.id);
            payload.data = upRes;
            socket.to(upRes.chatId.toString()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error updating read status: ', error);
            payload.error = 'Error updating read status:' + error
            socket.emit(CHAT_EVENT, payload)
        }
    }

    async getAllUnReadMessages(payload: Payload, socket: Socket, tenantId: string, user: ChatUser) {
        try {
            const count = await this.messageService.getAllUnReadMessageCount(user.id, tenantId);
            payload.data = { unReadCount: count }
            socket.emit(CHAT_EVENT, payload)
        } catch (error) {
            this.logService.error('Error getting all unread count: ', error);
            payload.error = 'Error getting all unread count: ' + error
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async addReaction(payload: Payload, socket: Socket, user: ChatUser, tenantId: string) {
        try {
            this.logService.log('Adding reaction : ', payload, user)
            const { messageId, reaction, chatId, activityId } = payload.data;
            socket.join(chatId.toString().trim());

            // Get chat using chatId
            const chat = await this.chatService.findChatById(chatId);

            const addRes = await this.messageService.addReaction(user.id, user.jobProUserId, reaction, messageId)
            this.logService.log('AddRes: ', addRes)
            if (activityId) {
                addRes[activityId] = activityId
                payload.data = addRes;
            } else {
                payload.data = addRes;
            };

            socket.to(chatId.toString().trim()).emit(CHAT_EVENT, payload);
            socket.emit(CHAT_EVENT, payload);

            if (addRes.sender['id'] != user.id) {
                const activityObj: ChatActivityDto = {
                    sender: user.id,
                    chat: chatId,
                    message: messageId,
                    tenantId: tenantId,
                    activityType: ACTIVITY_TYPE.REACTION,
                    user: addRes.sender['id']
                }
                const activity = await this.activityService.createActivity(activityObj);
                const activityPayload: Payload = {
                    event: CHAT_EVENTS.NEW_ACTIVITY,
                    data: activity
                };
                // const userSockets = this.getUserSocketIds(addRes.sender['id'])
                // this.logService.log('user sockets: ', userSockets)

                socket.to(addRes.sender['id']?.toString()).emit(CHAT_EVENT, activityPayload);
                socket.emit(CHAT_EVENT, payload);
                this.sendNotificationToAllWithString(chatId, socket, 'You have a new reaction in a chat message in ' + chat.name + ' chat', 'New Reaction in a chat on Joble');
            }
        } catch (error) {
            this.logService.error('Error adding reaction: ', error);
            payload.error = 'Error adding reaction: ' + error
            socket.emit(CHAT_EVENT, payload)
        }
    }

    async removeReaction(payload: Payload, socket: Socket, user: ChatUser) {
        try {
            const { messageId, reaction, chatId } = payload.data;
            // this.logService.log('remove reaction payload: ', payload.data, user)
            const remRes = await this.messageService.removeReaction(user.id, user.jobProUserId, reaction, messageId)
            payload.data = remRes;
            socket.to(chatId).emit(CHAT_EVENT, payload)
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error removing reaction: ', error);
            payload.error = 'Error removing reaction: ' + error
            socket.emit(CHAT_EVENT, payload)
        }
    }


    async getChatMessages(payload: Payload, socket: Socket) {
        try {
            const { page, pageSize, chatId } = payload.data;
            const messages = await this.messageService.getPaginatedMessages(chatId, page, pageSize,)
            payload.data = messages;
            // this.logService.log('paged messages:', messages)
            socket.emit(CHAT_EVENT, payload);
        } catch (error) {
            this.logService.error('Error getting chat messsages: ', error);

            payload.error = 'Error getting chat messages: ' + error
            socket.emit(CHAT_EVENT, payload)
        }
    }


    /** 
     * 
     * @param userId 
     * @param tenantId 
     * @param jobProUserId
     * @param socket
     * Return Chat[]  returns a list of all user chats including public chats he is yet to join
     */

    async getUserChats(socket: Socket, tenantId: string, userData: ChatUser) {
        try {

            socket.join(tenantId?.toString());
            // socket.emit(CHAT_EVENT, statusPayload);
            this.logService.log('TenantId in get user chat: ', tenantId);

            // const user = await this.chatUserService.findUserByJobProId(userData.jobProUserId);

            const { privateChats, publicChats } = await this.chatService.findChatsForUserWithUnReadCount(tenantId, userData.id);

            let totalUnReadCount = 0;
            // for public chats
            let chats = [];
            for (let i = 0; i < publicChats.length; i++) {
                const loadedMessages = await this.messageService.getAllUnReadWithLastMessageInChat(privateChats[i]?.toJSON().id.toString(), userData.id.toString());

                let counts = await Promise.all([this.messageService.getAllUnReadMessageCountPerChat(userData.id, tenantId, publicChats[i]?.id ?? ''), this.messageService.findTotalMessageCountForAChat(publicChats[i].id.toString())],);
                totalUnReadCount += counts[0];
                const chat = {
                    ...publicChats[i].toJSON(),
                    unReadMessageCount: counts[0],
                    messageCount: counts[1],
                    pageNumber: 0,
                    loadedMessages,
                };
                chats.push(chat)
                // const payload: Payload = {
                //     event: CHAT_EVENTS.CHAT_CREATED,
                //     data: chat
                // }
                // socket.emit(CHAT_EVENT, payload);

            }

            // for private chats 
            for (let i = 0; i < privateChats.length; i++) {
                let counts = await Promise.all([this.messageService.getAllUnReadMessageCountPerChat(userData.id, tenantId, privateChats[i].id), this.messageService.findTotalMessageCountForAChat(privateChats[i].id.toString())],);
                totalUnReadCount += counts[0];
                const loadedMessages = await this.messageService.getAllUnReadWithLastMessageInChat(privateChats[i]?.toJSON().id.toString(), userData.id.toString())

                const chat = {
                    ...privateChats[i].toJSON(),
                    unReadMessageCount: counts[0],
                    messageCount: counts[1],
                    pageNumber: 0,
                    loadedMessages,
                };
                chats.push(chat);
                // chat.unReadMessageCount = counts[0];
                // totalUnReadCount += counts[0];

                chat.unReadMessageCount = counts[0];
                const payload: Payload = {
                    event: CHAT_EVENTS.CHAT_CREATED,
                    data: chat
                }
                // socket.emit(CHAT_EVENT, payload);

                // this.logService.log('updated chat: ', chat.id);
            }

            const allChatsPayload: Payload = {
                event: CHAT_EVENTS.ALL_CHATS,
                data: Array.from(new Set([...chats])).sort((a, b) => b.lastMessageTimestamp - a.lastMessageTimestamp)
            }
            socket.emit(CHAT_EVENT, allChatsPayload);

            const onlineUsers = this.getUsersByTenantId(tenantId);
            const onlineUsersPayload: Payload = {
                data: onlineUsers,
                event: CHAT_EVENTS.ONLINE_USERS
            }
            socket.emit(CHAT_EVENT, onlineUsersPayload);

            const unReadCountPayload: Payload = {
                event: CHAT_EVENTS.GET_TOTAL_UNREAD_COUNT,
                data: totalUnReadCount
            }

            socket.emit(CHAT_EVENT, unReadCountPayload);


        } catch (error) {
            this.logService.error('Error getting user chats: ', error);
            const payload: Payload = {
                data: null,
                event: 'ALL_CHATS',
                error: error
            }
            socket.emit(CHAT_EVENT, payload)
        }
    }
    async sendNotificationToAllWithString(chatId: string, socket?: Socket, message?: string, subject?: string, messageData?: ChatMessageDto, senderUser?: ChatUserDto | ChatUser) {
        // this.logService.log('Chat is string: ', chatId)
        const foundChat = await this.chatService.findChatByIdPopulated(chatId);
        for (const user of foundChat.users) {
            var chatUser = JSON.parse(JSON.stringify(user));
            this.sendNotification(user['id'], foundChat.id, foundChat.tenantId, foundChat.name, chatUser, message, socket, subject, messageData, senderUser);
        }
    }
    async sendNotificationToAll(chat: ChatDto, socket: Socket, messageData?: ChatMessageDto, senderUser?: ChatUserDto | ChatUser) {

        this.logService.log('Chat user string: ', chat.users)
        for (const user of chat.users) {
            const chatUser = JSON.parse(JSON.stringify(user));
            this.sendNotification(chatUser.id, chat.id, chat.tenantId, chat.name, chatUser, null, socket, null, messageData, senderUser)
        }
    }

    async sendNotification(userId: string, chatId: string, tenantId: string, chatName: string, user?: ChatUserDto | ChatUser, message?: string, socket?: Socket, subject?: string, messageData?: ChatMessageDto, senderUser?: ChatUserDto | ChatUser) {
        try {
            let authToken;
            if (socket)
                authToken = this.extractAuthToken(socket);

            // Get chat details using chatId
            const chat = await this.chatService.findChatById(chatId);

            if (!user) {
                user = await this.chatUserService.findUser(userId);
            }
            const onlineUsers = this.getUsersByTenantId(tenantId)
            if (!onlineUsers.find(u => u.userId == userId) || user.activeChatId !== chatId) {
                // user is not online;

                // Determine notification body based on message type and content
                let notificationBody = message;
                let notificationSubject = subject;

                if (messageData && !message) {
                    // Determine message type
                    let messageType = MESSAGE_TYPE.TEXT; // default
                    if (messageData.files?.length && !messageData.text) {
                        messageType = MESSAGE_TYPE.FILE;
                    } else if (messageData.files?.length && messageData.text) {
                        messageType = MESSAGE_TYPE.TEXT_WITH_FILE;
                    }

                    // Set notification body based on message type
                    if (messageType === MESSAGE_TYPE.TEXT || messageType === MESSAGE_TYPE.TEXT_WITH_FILE) {
                        notificationBody = messageData.text || message || "You have an unread chat message in " + chatName + ' chat';
                    } else if (messageType === MESSAGE_TYPE.FILE) {
                        // Get file names
                        const fileNames = messageData.files?.map(file => file.originalname || file.filename).filter(Boolean) || [];
                        if (fileNames.length > 0) {
                            notificationBody = fileNames.join(', ');
                        } else {
                            notificationBody = "File(s) received";
                        }
                    }
                }

                // Set notification subject based on sender
                if (!notificationSubject && senderUser) {
                    const senderDisplayName = senderUser.jobProUserId || subject;
                    notificationSubject = senderDisplayName;
                } else if (!notificationSubject) {
                    notificationSubject = subject || "You have an unread chat message in " + chatName + ' chat';
                }

                // Fallback for notification body
                if (!notificationBody) {
                    notificationBody = "You have an unread message in " + chatName + ' chat';
                }

                const notification = new NotificationMessageModel();
                notification.application = Applications.Joble,
                notification.templateWithPlaceHolders = {
                    template: null,
                    props: {
                        message: notificationBody,
                        chatId: chatId,
                        chatName: chatName,
                        tenantId: tenantId,
                        isPrivate: chat.isPrivate.toString(),
                        chatType: chat.chatType,
                        users: JSON.stringify(chat.jobProUserIds),
                        isGeneral: chat.isGeneral.toString(),
                        key: "chat",
                    },
                    additionalProps: chat
                }
                notification.body = notificationBody;
                notification.userId = user.jobProUserId;
                notification.subject = notificationSubject;
                notification.notificationTypes = [NotificationType.Push]

                // this.logService.log('notification: ', notification);
                this.rabbitMQService.sendNotification(RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE, JSON.stringify(notification), authToken, socket);

            }
        } catch (error) {
            this.logService.error('send notification error: ', error)
            const payload: Payload = {
                data: null,
                event: 'MESSAGE_NOTIFICATION',
                error: error
            }
            socket.emit(CHAT_EVENT, payload)
        }
    }

    async sendEmailNotification(userId: string, subject: string, user?: ChatUserDto | ChatUser, htmlBody?: string, socket?: Socket) {
        try {
            let authToken;
            if (socket)
                authToken = this.extractAuthToken(socket);

            if (!user) {
                user = await this.chatUserService.findUser(userId);
            }
            
            const notification = new NotificationMessageModel();
                notification.application = Applications.Joble,
                    notification.templateWithParams = htmlBody
                    notification.subject = subject
                    notification.notificationTypes = [NotificationType.Email]
                    notification.recipientEmails = [user.email]
                this.logService.log('notification: ', notification);
                this.rabbitMQService.sendNotification(RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE, JSON.stringify(notification), authToken, socket);

        } catch (error) {
            this.logService.error('send notification error: ', error)
            const payload: Payload = {
                data: null,
                event: 'MESSAGE_NOTIFICATION',
                error: error
            }
            socket.emit(CHAT_EVENT, payload)
        }
    }

    extractAuthToken(socket: Socket) {
        let authorization = socket.handshake.auth.authorization;
        if (authorization.includes(' ')) {
            const array = authorization?.split(' ');
            authorization = array.length > 1 ? array[1] : array[0];
        }
        // this.logService.log('authorization: ', authorization);
        return authorization ? authorization : undefined;

    }
    getUsersByTenantId(tenantId: string): { userId: string, status: string, }[] {
        const matchingUsers = [];

        // Iterate over each entry in the users map
        this.users.forEach((userArray, userId) => {
            // Filter users with the matching tenantId and add them to the result
            const filteredUsers = userArray
                .filter(user => user.tenantId === tenantId)
                .map(user => ({ userId: userId, jobProUserId: user.jobProUserId, status: 'ONLINE' })); // Map to include user ID

            matchingUsers.push(...filteredUsers);
        });

        return matchingUsers;

    }
    getUserBySocket(socketId: string): { userId: string, jobProUserId: string, tenantid: string } | undefined {
        for (const [userId, data] of this.users.entries()) {
            for (const socket of data) {
                if (socket.socket.id === socketId) {
                    return { userId, jobProUserId: socket.jobProUserId, tenantid: socket.tenantId };
                }
            }
        }
        return undefined; // Return undefined if no user is found
    }

    getUserSocketIds(userId: string) {
        this.logService.log('all users: ', this.users, 'userId: ', userId)
        const user = this.users.get(userId);
        this.logService.log('sockets found: ', user, ' userId used: ', userId);
        if (user?.length) {
            return user?.map(socket => socket.socket.id)
        } else
            return [];
    }

    checkUserSocket(socket: Socket, userId: string, tenantId: string, jobProUserId: string) {
        try {
            this.logService.log('socket and userId 1: ', socket.id, userId);
            if (this.users.has(userId)) {
                if (!this.users.get(userId).find(data => data.socket.id == socket.id))
                    this.users.get(userId).push({ socket, tenantId, jobProUserId });
            } else {
                this.users.set(userId, [{ socket, tenantId, jobProUserId }]);
            }
            this.logService.log('users with sockets: ', this.users)
        } catch (error) {
            this.logService.error('error checking user socket id')
        }
    }

    // Development/Testing message handler without authentication
    @SubscribeMessage('test-event')
    handleTestEvent(@MessageBody() payload: Payload, @ConnectedSocket() socket: Socket) {
        console.log('Received test payload: ', payload);
        this.logService.log('Test event payload from client: ', payload);
        
        // For development testing, create a mock user from query parameters
        const { userId, tenantId, jobProUserId } = socket.handshake.query;
        const email = socket.handshake.query.email || '<EMAIL>';
        
        console.log('Test mode - Query params:', { userId, tenantId, jobProUserId, email });
        this.logService.log('Test mode - Query params:', { userId, tenantId, jobProUserId, email });
        
        // Create or find test user
        this.handleTestMode(payload, socket, userId as string, tenantId as string, jobProUserId as string, email as string);
    }

    private async handleTestMode(payload: Payload, socket: Socket, userId: string, tenantId: string, jobProUserId: string, email: string) {
        try {
            // Try to find or create a test user
            let user = await this.chatUserService.findUserByJobProId(jobProUserId);
            if (!user) {
                user = await this.chatUserService.createUser({ 
                    jobProUserId, 
                    email: email,
                    tenantId: tenantId 
                });
                this.logService.log('Created test user:', user);
            }

            // Set up socket with test user
            socket['user'] = user;
            socket['tenantId'] = tenantId;
            socket['jobProUserId'] = jobProUserId;
            
            // Join necessary rooms
            socket.join(tenantId);
            socket.join(user.id.toString());
            
            // Handle the event using the same switch logic as authenticated events
            switch (payload.event) {
                case CHAT_EVENTS.GET_USER_DATA:
                    await this.getUserData(payload, socket, tenantId, user);
                    break;
                case CHAT_EVENTS.GET_CHATS_WITH_UNREAD_COUNT:
                    await this.getUserChats(socket, tenantId, user);
                    break;
                case CHAT_EVENTS.GET_CHAT_PAGINATED_MESSAGES:
                    await this.getChatMessages(payload, socket);
                    break;
                case CHAT_EVENTS.NEW_MESSAGE:
                    await this.createNewMessage(payload, socket, user, tenantId);
                    break;
                case CHAT_EVENTS.CREATE_CHAT:
                    await this.createChat(payload, socket, tenantId, user);
                    break;
                case CHAT_EVENTS.UPDATE_MESSAGE:
                    await this.updateMessage(payload, socket);
                    break;
                case CHAT_EVENTS.DELETE_MESSAGE:
                    await this.deleteMessage(payload, socket, user);
                    break;
                case CHAT_EVENTS.PIN_UNPIN_MESSAGE:
                    await this.pinUnPinMessage(payload, socket, user);
                    break;
                case CHAT_EVENTS.PIN_CHAT:
                    await this.pinUnpinChat(payload, socket, user);
                    break;
                case CHAT_EVENTS.ADD_REACTION_TO_MESSAGE:
                    await this.addReaction(payload, socket, user, tenantId);
                    break;
                case CHAT_EVENTS.REMOVE_REACTION_FROM_MESSAGE:
                    await this.removeReaction(payload, socket, user);
                    break;
                case CHAT_EVENTS.UPDATE_READ_STATUS:
                    await this.updateReadStatus(payload, socket, user);
                    break;
                case CHAT_EVENTS.GET_ALL_UNREAD_MESSAGES:
                    await this.getAllUnReadMessages(payload, socket, tenantId, user);
                    break;
                case CHAT_EVENTS.IS_TYPING:
                    this.isTyping(payload, socket);
                    break;
                case CHAT_EVENTS.INITIATE_CALL:
                    this.initiateCall(payload, socket);
                    break;
                case CHAT_EVENTS.ANSwER_CALL:
                    this.answerCall(payload, socket, user);
                    break;
                case CHAT_EVENTS.DECLINE_CALL:
                    this.declineCall(payload, socket, user);
                    break;
                case CHAT_EVENTS.END_CALL:
                    this.endCall(payload, socket);
                    break;
                case CHAT_EVENTS.CALL_RINGING:
                    this.handleCallRinging(payload, socket);
                    break;
                case CHAT_EVENTS.MISSED_CALL:
                    this.handleMissCall(payload, socket);
                    break;
                case CHAT_EVENTS.CALL_CANCELED:
                    this.cancelCall(payload, socket, user);
                    break;
                case CHAT_EVENTS.IS_RECORDING:
                    this.isRecording(payload, socket);
                    break;
                case CHAT_EVENTS.ADD_USERS_TO_CHAT:
                    await this.addUsersToChat(payload, socket, user, tenantId);
                    break;
                case CHAT_EVENTS.REMOVE_USER_FROM_CHAT:
                    await this.removeUserFromChat(payload, socket, user, tenantId);
                    break;
                case CHAT_EVENTS.UPDATE_CHAT:
                    await this.updateChat(payload, socket, user);
                    break;
                case CHAT_EVENTS.BUSY:
                    this.isBusy(payload, socket, user);
                    break;
                case CHAT_EVENTS.IGNORE_CALL:
                    this.ignoreCall(payload, socket);
                    break;
                default:
                    this.logService.log('Unknown test event:', payload.event);
                    socket.emit('test-event', { 
                        event: payload.event, 
                        error: 'Unknown event type',
                        data: null 
                    });
            }
        } catch (error) {
            this.logService.error('Error in test mode:', error);
            socket.emit('test-event', { 
                event: payload.event, 
                error: 'Error processing test event: ' + error.message,
                data: null 
            });
        }
    }

}